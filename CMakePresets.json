{"version": 3, "configurePresets": [{"name": "default", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "toolchainFile": "${sourceDir}/cmake/gcc-arm-none-eabi.cmake", "cacheVariables": {}}, {"name": "Debug", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "RelWithDebInfo", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}}, {"name": "Release", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "MinSizeRel", "inherits": "default", "cacheVariables": {"CMAKE_BUILD_TYPE": "MinSizeRel"}}], "buildPresets": [{"name": "Debug", "configurePreset": "Debug"}, {"name": "RelWithDebInfo", "configurePreset": "RelWithDebInfo"}, {"name": "Release", "configurePreset": "Release"}, {"name": "MinSizeRel", "configurePreset": "MinSizeRel"}]}